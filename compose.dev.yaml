services:
  traefik:
    image: traefik:v2.9
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
    ports:
      - "80:80"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - laravel-development
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.localhost`)"
      - "traefik.http.routers.traefik.entrypoints=web"
      - "traefik.http.services.traefik.loadbalancer.server.port=8080"

  web:
    image: nginx:alpine
    volumes:
      - ./workspace/dashboard/:/var/www
      - ./docker/development/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - laravel-development
    depends_on:
      php-fpm:
        condition: service_started
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.app.rule=Host(`localhost`) || Host(`app.localhost`)"
      - "traefik.http.routers.app.entrypoints=web"
      - "traefik.http.services.app.loadbalancer.server.port=80"

  php-fpm:
    build:
      context: .
      dockerfile: ./docker/common/php-fpm/Dockerfile
      target: development
      args:
        UID: ${UID:-1000}
        GID: ${GID:-1000}
        XDEBUG_ENABLED: ${XDEBUG_ENABLED:-true}
        XDEBUG_MODE: develop,coverage,debug,profile
        XDEBUG_HOST: ${XDEBUG_HOST:-host.docker.internal}
        XDEBUG_IDE_KEY: ${XDEBUG_IDE_KEY:-DOCKER}
        XDEBUG_LOG: /dev/stdout
        XDEBUG_LOG_LEVEL: 0
    env_file:
      - ./workspace/dashboard/.env
    user: "${UID:-1000}:${GID:-1000}"
    volumes:
      - ./workspace/dashboard/:/var/www
    networks:
      - laravel-development
    depends_on:
      postgres:
        condition: service_started

  workspace:
    build:
      context: .
      dockerfile: ./docker/development/workspace/Dockerfile
      args:
        UID: ${UID:-1000}
        GID: ${GID:-1000}
        XDEBUG_ENABLED: ${XDEBUG_ENABLED:-true}
        XDEBUG_MODE: develop,coverage,debug,profile
        XDEBUG_HOST: ${XDEBUG_HOST:-host.docker.internal}
        XDEBUG_IDE_KEY: ${XDEBUG_IDE_KEY:-DOCKER}
        XDEBUG_LOG: /dev/stdout
        XDEBUG_LOG_LEVEL: 0
    tty: true  # Enables an interactive terminal
    stdin_open: true  # Keeps standard input open for 'docker exec'
    env_file:
      - ./workspace/dashboard/.env
    volumes:
      - ./workspace/dashboard/:/var/www
    networks:
      - laravel-development
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.vite.rule=Host(`vite.localhost`)"
      - "traefik.http.routers.vite.entrypoints=web"
      - "traefik.http.services.vite.loadbalancer.server.port=5173"

  postgres:
    image: postgres:16
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    environment:
      - POSTGRES_DB=sasadu
      - POSTGRES_USER=sasadu
      - POSTGRES_PASSWORD=secret
    volumes:
      - postgres-data-development:/var/lib/postgresql/data
    networks:
      - laravel-development

  redis:
    image: redis:alpine
    networks:
      - laravel-development

networks:
  laravel-development:
    driver: bridge

volumes:
  postgres-data-development: