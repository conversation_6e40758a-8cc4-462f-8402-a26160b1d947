services:
  traefik:
    image: traefik:v3.0
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
    ports:
      - "80:80"
      - "8080:8080"  # Traefik web
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - laravel-development
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.localhost`)"
      - "traefik.http.routers.traefik.entrypoints=web"
      - "traefik.http.services.traefik.loadbalancer.server.port=8080"

  web:
    image: nginx:alpine
    volumes:
      - ./workspace/web/:/var/www
      - ./docker/development/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - laravel-development
    depends_on:
      php-fpm:
        condition: service_started
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.app.rule=Host(`web.localhost`)"
      - "traefik.http.routers.app.entrypoints=web"
      - "traefik.http.services.app.loadbalancer.server.port=80"

  php-fpm:
    build:
      context: .
      dockerfile: ./docker/common/php-fpm/Dockerfile
      target: development
      args:
        UID: ${UID:-1000}
        GID: ${GID:-1000}
        XDEBUG_ENABLED: ${XDEBUG_ENABLED:-true}
        XDEBUG_MODE: develop,coverage,debug,profile
        XDEBUG_HOST: ${XDEBUG_HOST:-host.docker.internal}
        XDEBUG_IDE_KEY: ${XDEBUG_IDE_KEY:-DOCKER}
        XDEBUG_LOG: /dev/stdout
        XDEBUG_LOG_LEVEL: 0
    env_file:
      - ./workspace/web/.env
    user: "${UID:-1000}:${GID:-1000}"
    volumes:
      - ./workspace/web/:/var/www
    networks:
      - laravel-development
    depends_on:
      postgres:
        condition: service_started

  workspace:
    build:
      context: .
      dockerfile: ./docker/development/workspace/Dockerfile
      args:
        UID: ${UID:-1000}
        GID: ${GID:-1000}
        XDEBUG_ENABLED: ${XDEBUG_ENABLED:-true}
        XDEBUG_MODE: develop,coverage,debug,profile
        XDEBUG_HOST: ${XDEBUG_HOST:-host.docker.internal}
        XDEBUG_IDE_KEY: ${XDEBUG_IDE_KEY:-DOCKER}
        XDEBUG_LOG: /dev/stdout
        XDEBUG_LOG_LEVEL: 0
    tty: true  # Enables an interactive terminal
    stdin_open: true  # Keeps standard input open for 'docker exec'
    ports:
      - "${VITE_PORT:-5173}:5173"
    env_file:
      - ./workspace/web/.env
    volumes:
      - ./workspace/web/:/var/www
    networks:
      - laravel-development
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.vite.rule=Host(`vite.localhost`)"
      - "traefik.http.routers.vite.entrypoints=web"
      - "traefik.http.services.vite.loadbalancer.server.port=5173"

  postgres:
    image: postgres:16
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    environment:
      - POSTGRES_DB=sasadu
      - POSTGRES_USER=sasadu
      - POSTGRES_PASSWORD=secret
    volumes:
      - postgres-data-development:/var/lib/postgresql/data
    networks:
      - laravel-development

  redis:
    image: redis:alpine
    networks:
      - laravel-development

  n8n:
    image: n8nio/n8n:latest
    ports:
      - "${N8N_PORT:-5678}:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=${N8N_BASIC_AUTH_USER:-admin}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_BASIC_AUTH_PASSWORD:-password}
      - N8N_HOST=${N8N_HOST:-0.0.0.0}
      - N8N_PORT=5678
      - N8N_PROTOCOL=${N8N_PROTOCOL:-http}
      - N8N_EDITOR_BASE_URL=${N8N_EDITOR_BASE_URL:-http://localhost:5678}
      - WEBHOOK_URL=${WEBHOOK_URL:-http://localhost:5678}
      - N8N_SECURE_COOKIE=false
      - N8N_COOKIES_SAME_SITE=lax
      - NODE_ENV=development
      - GENERIC_TIMEZONE=${TZ:-UTC}
      - N8N_METRICS=false
      - N8N_DIAGNOSTICS_ENABLED=false
    volumes:
      - n8n-data-development:/home/<USER>/.n8n
    networks:
      - laravel-development
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.n8n.rule=Host(`n8n.localhost`)"
      - "traefik.http.routers.n8n.entrypoints=web"
      - "traefik.http.services.n8n.loadbalancer.server.port=5678"

networks:
  laravel-development:
    driver: bridge

volumes:
  postgres-data-development:
  n8n-data-development: