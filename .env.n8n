# n8n Configuration for External Access
# Copy this to your main .env file or source it

# Basic Configuration
N8N_PORT=5678
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=your_secure_password_here

# For Local Development
N8N_HOST=0.0.0.0
N8N_PROTOCOL=http
N8N_EDITOR_BASE_URL=http://localhost:5678
WEBHOOK_URL=http://localhost:5678

# For ngrok (uncomment and update when using ngrok)
# N8N_PROTOCOL=https
# N8N_EDITOR_BASE_URL=https://your-ngrok-subdomain.ngrok.io
# WEBHOOK_URL=https://your-ngrok-subdomain.ngrok.io

# For Production/Custom Domain (uncomment and update when needed)
# N8N_PROTOCOL=https
# N8N_EDITOR_BASE_URL=https://your-domain.com
# WEBHOOK_URL=https://your-domain.com

# Security Settings
N8N_SECURE_COOKIE=false
N8N_COOKIES_SAME_SITE=lax

# Optional Settings
TZ=UTC
N8N_METRICS=false
N8N_DIAGNOSTICS_ENABLED=false
