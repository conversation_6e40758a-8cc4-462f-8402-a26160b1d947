# n8n Setup Guide untuk External Access

## 🎯 Overview

n8n telah berhasil ditambahkan ke Docker Compose setup Anda dengan konfigurasi yang mendukung external access melalui ngrok atau domain custom.

## 🔧 Konfigurasi yang Sudah Diterapkan

### Environment Variables yang Penting:
- `N8N_SECURE_COOKIE=false` - Mengatasi error secure cookie
- `N8N_COOKIES_SAME_SITE=lax` - Mengatur cookie policy
- `N8N_HOST=0.0.0.0` - Memungkinkan akses dari luar
- `N8N_PROTOCOL` - http/https sesuai kebutuhan
- `N8N_EDITOR_BASE_URL` - URL untuk akses editor
- `WEBHOOK_URL` - URL untuk webhook endpoints

## 🚀 Cara Menggunakan dengan ngrok

### 1. Install ngrok (jika belum)
```bash
# Ubuntu/Debian
sudo snap install ngrok

# Atau download dari https://ngrok.com/download
```

### 2. <PERSON><PERSON><PERSON>t Setup
```bash
./setup-ngrok.sh
```

### 3. Manual Setup
```bash
# 1. Start ngrok tunnel
ngrok http 5678

# 2. Copy ngrok URL (contoh: https://abc123.ngrok.io)

# 3. Update environment variables
export N8N_PROTOCOL=https
export N8N_EDITOR_BASE_URL=https://abc123.ngrok.io
export WEBHOOK_URL=https://abc123.ngrok.io

# 4. Restart n8n
docker compose -f compose.dev.yaml restart n8n
```

## 🌐 Akses n8n

### Local Access:
- **Direct**: http://localhost:5678
- **Via Traefik**: http://n8n.localhost
- **Username**: admin
- **Password**: password (atau sesuai .env)

### External Access (via ngrok):
- **URL**: https://your-ngrok-url.ngrok.io
- **Username**: admin
- **Password**: password

## 🔐 Keamanan

### Untuk Testing:
- Default credentials sudah aman untuk testing
- ngrok URL bersifat temporary dan random

### Untuk Production:
1. Ganti password default:
   ```bash
   export N8N_BASIC_AUTH_PASSWORD=your_secure_password
   ```

2. Gunakan domain sendiri dengan SSL:
   ```bash
   export N8N_PROTOCOL=https
   export N8N_EDITOR_BASE_URL=https://your-domain.com
   export WEBHOOK_URL=https://your-domain.com
   ```

## 🔄 Webhook Configuration

### Untuk ngrok:
- Webhook URL akan otomatis menggunakan ngrok URL
- Semua webhook akan accessible dari internet
- Cocok untuk testing integrasi external services

### Untuk Production:
- Gunakan domain yang persistent
- Setup SSL certificate
- Configure firewall rules

## 🛠️ Troubleshooting

### Error "secure cookie":
✅ **Sudah diperbaiki** dengan `N8N_SECURE_COOKIE=false`

### Tidak bisa akses dari luar:
- Pastikan `N8N_HOST=0.0.0.0`
- Check firewall settings
- Verify ngrok tunnel aktif

### Webhook tidak bekerja:
- Pastikan `WEBHOOK_URL` sesuai dengan external URL
- Check n8n logs: `docker compose -f compose.dev.yaml logs n8n`

## 📝 File Konfigurasi

### .env.n8n
Template konfigurasi untuk berbagai scenario

### .env.ngrok
Generated otomatis oleh setup script untuk ngrok

### .env.local
Generated otomatis oleh setup script untuk local development

## 🎛️ Advanced Configuration

### Custom Domain:
```yaml
environment:
  - N8N_PROTOCOL=https
  - N8N_EDITOR_BASE_URL=https://n8n.yourdomain.com
  - WEBHOOK_URL=https://n8n.yourdomain.com
  - N8N_SECURE_COOKIE=true  # Enable untuk HTTPS
```

### Database Integration:
n8n menggunakan SQLite by default, tapi bisa dikonfigurasi untuk PostgreSQL:
```yaml
environment:
  - DB_TYPE=postgresdb
  - DB_POSTGRESDB_HOST=postgres
  - DB_POSTGRESDB_DATABASE=n8n
  - DB_POSTGRESDB_USER=n8n_user
  - DB_POSTGRESDB_PASSWORD=n8n_password
```

## 🔍 Monitoring

### Check Status:
```bash
docker compose -f compose.dev.yaml ps n8n
```

### View Logs:
```bash
docker compose -f compose.dev.yaml logs -f n8n
```

### Check Configuration:
```bash
./setup-ngrok.sh  # Option 4
```
