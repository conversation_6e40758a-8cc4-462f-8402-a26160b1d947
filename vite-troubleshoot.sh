#!/bin/bash

echo "🔍 Vite Troubleshooting Script"
echo "=============================="

echo ""
echo "📊 Container Status:"
docker compose -f compose.dev.yaml ps workspace

echo ""
echo "🌐 Port Check:"
echo "  - Direct port 5173:"
timeout 3 curl -s http://localhost:5173 > /dev/null && echo "    ✅ localhost:5173 accessible" || echo "    ❌ localhost:5173 not accessible"

echo "  - Via Traefik:"
timeout 3 curl -s http://vite.localhost > /dev/null && echo "    ✅ vite.localhost accessible" || echo "    ❌ vite.localhost not accessible"

echo ""
echo "🔧 Traefik Configuration:"
echo "  Checking Traefik labels for workspace service..."
docker compose -f compose.dev.yaml config | grep -A 10 -B 5 "traefik.*vite"

echo ""
echo "📋 Network Information:"
echo "  Workspace container IP:"
docker inspect sasadu-workspace-1 2>/dev/null | jq -r '.[0].NetworkSettings.Networks[].IPAddress' 2>/dev/null || echo "    Container not running"

echo ""
echo "🚀 Vite Process Check:"
echo "  Checking if Vite is running in container..."
docker compose -f compose.dev.yaml exec workspace bash -c "ps aux | grep vite" 2>/dev/null || echo "    Cannot check - container may not be running"

echo ""
echo "💡 Quick Fixes:"
echo "1. Restart Vite with correct config:"
echo "   docker compose -f compose.dev.yaml exec workspace bash -c 'source ~/.bashrc && cd /var/www && npm run dev'"
echo ""
echo "2. Test direct container access:"
echo "   CONTAINER_IP=\$(docker inspect sasadu-workspace-1 | jq -r '.[0].NetworkSettings.Networks[].IPAddress')"
echo "   curl http://\$CONTAINER_IP:5173"
echo ""
echo "3. Check Traefik dashboard:"
echo "   http://localhost:8080"
echo ""
echo "4. Alternative: Use direct port access:"
echo "   http://localhost:5173 (if port is exposed)"
