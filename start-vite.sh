#!/bin/bash

echo "🚀 Starting Vite Development Server"
echo "==================================="

# Check if workspace container is running
if ! docker compose -f compose.dev.yaml ps workspace | grep -q "Up"; then
    echo "❌ Workspace container is not running"
    echo "   Starting workspace container..."
    docker compose -f compose.dev.yaml up -d workspace
    sleep 3
fi

echo "✅ Workspace container is running"

# Check if port 5173 is exposed
if ! docker compose -f compose.dev.yaml ps workspace | grep -q "5173->5173"; then
    echo "❌ Port 5173 is not exposed"
    echo "   Please restart workspace container with updated compose.dev.yaml"
    exit 1
fi

echo "✅ Port 5173 is exposed"

echo ""
echo "🔧 Starting Vite with host binding..."
echo "   This will run Vite with --host 0.0.0.0 to allow external access"
echo "   Note: Vite will run in foreground. Press Ctrl+C to stop."
echo ""

# Start Vite in the foreground
docker compose -f compose.dev.yaml exec workspace bash -c "source ~/.bashrc && cd /var/www && npm run dev:host"

# Note: The script will exit here as Vite runs in foreground
# The following lines are for reference only

echo ""
echo "📋 Access URLs (when Vite is running):"
echo "   - Via Traefik: http://vite.localhost"
echo "   - Direct Port: http://localhost:5173"
echo "   - Laravel App: http://web.localhost"
echo ""
echo "🔍 To view Vite logs in another terminal:"
echo "   docker compose -f compose.dev.yaml logs -f workspace"
echo ""
echo "🛑 To stop Vite:"
echo "   Press Ctrl+C in this terminal"
echo "   Or: docker compose -f compose.dev.yaml exec workspace pkill -f vite"
