#!/bin/bash

echo "🚀 Starting Vite Development Server"
echo "==================================="

# Check if workspace container is running
if ! docker compose -f compose.dev.yaml ps workspace | grep -q "Up"; then
    echo "❌ Workspace container is not running"
    echo "   Starting workspace container..."
    docker compose -f compose.dev.yaml up -d workspace
    sleep 3
fi

echo "✅ Workspace container is running"

# Check if port 5173 is exposed
if ! docker compose -f compose.dev.yaml ps workspace | grep -q "5173->5173"; then
    echo "❌ Port 5173 is not exposed"
    echo "   Please restart workspace container with updated compose.dev.yaml"
    exit 1
fi

echo "✅ Port 5173 is exposed"

echo ""
echo "🔧 Starting Vite with host binding..."
echo "   This will run Vite with --host 0.0.0.0 to allow external access"
echo ""

# Start Vite in the background
docker compose -f compose.dev.yaml exec -d workspace bash -c "source ~/.bashrc && cd /var/www && npm run dev:host"

echo "⏳ Waiting for Vite to start..."
sleep 5

# Check if Vite is accessible
echo "🌐 Testing access..."
if curl -s http://vite.localhost > /dev/null; then
    echo "✅ Vite is accessible at http://vite.localhost"
else
    echo "❌ Vite is not accessible at http://vite.localhost"
fi

if curl -s http://localhost:5173 > /dev/null; then
    echo "✅ Vite is accessible at http://localhost:5173"
else
    echo "❌ Vite is not accessible at http://localhost:5173"
fi

echo ""
echo "📋 Access URLs:"
echo "   - Via Traefik: http://vite.localhost"
echo "   - Direct Port: http://localhost:5173"
echo ""
echo "🔍 To view Vite logs:"
echo "   docker compose -f compose.dev.yaml logs -f workspace"
echo ""
echo "🛑 To stop Vite:"
echo "   docker compose -f compose.dev.yaml exec workspace pkill -f vite"
