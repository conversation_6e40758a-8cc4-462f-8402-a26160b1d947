# 🚀 Vite Development Server Setup

## ✅ Status: BERHASIL!

Vite development server sudah berhasil dikonfigurasi dan ber<PERSON><PERSON> dengan benar.

## 🌐 Access URLs

- **Via Traefik**: http://vite.localhost
- **Direct Port**: http://localhost:5173
- **<PERSON>vel App**: http://web.localhost

## 📁 Struktur Direktori

```
workspace/
└── web/                    # Laravel application directory
    ├── vite.config.js     # Vite configuration
    ├── package.json       # NPM scripts
    └── ...
```

## ⚙️ Konfigurasi

### 1. <PERSON>er Compose (compose.dev.yaml)
```yaml
workspace:
  ports:
    - "5173:5173"  # Vite port exposed
  labels:
    - "traefik.http.routers.vite.rule=Host(`vite.localhost`)"
    - "traefik.http.services.vite.loadbalancer.server.port=5173"
```

### 2. Vite Configuration (workspace/web/vite.config.js)
```javascript
export default defineConfig({
    server: {
        host: '0.0.0.0',        // Allow external access
        port: 5173,             # Port
        hmr: {
            host: 'vite.localhost',
            port: 5173,
        },
        watch: {
            usePolling: true,   # For Docker compatibility
        },
    },
});
```

### 3. NPM Scripts (workspace/web/package.json)
```json
{
    "scripts": {
        "dev": "vite",
        "dev:host": "vite --host 0.0.0.0 --port 5173"
    }
}
```

## 🚀 Cara Menjalankan

### Option 1: Menggunakan Script (Recommended)
```bash
./start-vite.sh
```

### Option 2: Manual
```bash
# Masuk ke workspace container
docker compose -f compose.dev.yaml exec workspace bash

# Di dalam container
source ~/.bashrc
cd /var/www
npm run dev:host
```

## 🔧 Troubleshooting

### Jika Port Tidak Ter-expose
```bash
# Restart workspace container
docker compose -f compose.dev.yaml restart workspace

# Cek status port
docker compose -f compose.dev.yaml ps workspace
```

### Jika Vite Tidak Accessible
```bash
# Cek apakah Vite berjalan dengan host binding
# Output harus menampilkan: Network: http://172.x.x.x:5173/

# Test akses
curl -I http://localhost:5173
curl -I http://vite.localhost
```

## 📋 Fitur yang Sudah Berfungsi

- ✅ **Hot Module Replacement (HMR)**
- ✅ **External Access** via 0.0.0.0 binding
- ✅ **Traefik Integration** via vite.localhost
- ✅ **Direct Port Access** via localhost:5173
- ✅ **Docker Compatibility** dengan polling
- ✅ **Laravel Integration** dengan Laravel Vite Plugin

## 🔍 Monitoring

### Melihat Logs
```bash
# Vite logs
docker compose -f compose.dev.yaml logs -f workspace

# Container status
docker compose -f compose.dev.yaml ps
```

### Stop Vite
```bash
# Jika running via script: Ctrl+C
# Atau kill process:
docker compose -f compose.dev.yaml exec workspace pkill -f vite
```

## 📝 Notes

1. **Path Change**: Direktori berubah dari `dashboard` ke `web`
2. **Host Binding**: Menggunakan `0.0.0.0` untuk external access
3. **HMR Configuration**: Dikonfigurasi untuk bekerja dengan Traefik
4. **Polling**: Enabled untuk compatibility dengan Docker volumes
