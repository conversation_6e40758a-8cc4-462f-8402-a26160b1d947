#!/bin/bash

# n8n ngrok Setup Script
# This script helps you configure n8n for external access via ngrok

echo "🚀 n8n ngrok Setup Script"
echo "========================="

# Check if ngrok is installed
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok is not installed. Please install ngrok first:"
    echo "   Visit: https://ngrok.com/download"
    echo "   Or use: snap install ngrok (on Ubuntu)"
    exit 1
fi

echo "✅ ngrok is installed"

# Get current ngrok tunnel info
echo ""
echo "📋 Current ngrok tunnels:"
curl -s http://localhost:4040/api/tunnels 2>/dev/null | jq -r '.tunnels[] | "  - \(.public_url) -> \(.config.addr)"' 2>/dev/null || echo "  No active tunnels found"

echo ""
echo "🔧 Setup Options:"
echo "1. Start ngrok tunnel for n8n (port 5678)"
echo "2. Update n8n environment for ngrok"
echo "3. Reset to local development"
echo "4. Show current configuration"
echo ""

read -p "Choose option (1-4): " choice

case $choice in
    1)
        echo ""
        echo "🌐 Starting ngrok tunnel for n8n..."
        echo "   This will expose http://localhost:5678 to the internet"
        echo "   Press Ctrl+C to stop the tunnel"
        echo ""
        ngrok http 5678
        ;;
    2)
        echo ""
        read -p "Enter your ngrok URL (e.g., https://abc123.ngrok.io): " ngrok_url
        
        if [[ -z "$ngrok_url" ]]; then
            echo "❌ No URL provided"
            exit 1
        fi
        
        # Remove trailing slash
        ngrok_url=${ngrok_url%/}
        
        echo ""
        echo "🔄 Updating n8n configuration for ngrok..."
        
        # Create or update environment variables
        cat > .env.ngrok << EOF
# n8n ngrok Configuration
N8N_PROTOCOL=https
N8N_EDITOR_BASE_URL=${ngrok_url}
WEBHOOK_URL=${ngrok_url}
N8N_SECURE_COOKIE=false
N8N_COOKIES_SAME_SITE=lax
EOF
        
        echo "✅ Configuration saved to .env.ngrok"
        echo ""
        echo "📝 To apply these settings:"
        echo "   1. Copy the variables from .env.ngrok to your main .env file, or"
        echo "   2. Source the file: source .env.ngrok"
        echo "   3. Restart n8n: docker compose -f compose.dev.yaml restart n8n"
        echo ""
        echo "🌐 Your n8n will be accessible at: ${ngrok_url}"
        ;;
    3)
        echo ""
        echo "🏠 Resetting to local development configuration..."
        
        cat > .env.local << EOF
# n8n Local Development Configuration
N8N_PROTOCOL=http
N8N_EDITOR_BASE_URL=http://localhost:5678
WEBHOOK_URL=http://localhost:5678
N8N_SECURE_COOKIE=false
N8N_COOKIES_SAME_SITE=lax
EOF
        
        echo "✅ Local configuration saved to .env.local"
        echo ""
        echo "📝 To apply these settings:"
        echo "   1. Copy the variables from .env.local to your main .env file, or"
        echo "   2. Source the file: source .env.local"
        echo "   3. Restart n8n: docker compose -f compose.dev.yaml restart n8n"
        ;;
    4)
        echo ""
        echo "📊 Current n8n Configuration:"
        echo "=============================="
        docker compose -f compose.dev.yaml exec n8n printenv | grep -E "^N8N_|^WEBHOOK_" | sort
        ;;
    *)
        echo "❌ Invalid option"
        exit 1
        ;;
esac

echo ""
echo "✨ Done!"
